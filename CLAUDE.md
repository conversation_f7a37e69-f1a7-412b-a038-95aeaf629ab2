# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目架构

这是一个基于 Rust `tokenizers` 库 (v0.21.4) 构建的多语言分词器包装器。项目通过多种语言绑定暴露分词功能：

### 核心组件
- **Rust 库** (`src/lib.rs`): 提供 C 兼容的 FFI 层，包含 `#[no_mangle]` 函数
- **Rust 模块** (`src/tokenizer_wrapper.rs`): 高级 Rust API，包含 `SimpleEncoding` 结构体
- **Go 绑定** (`go_binding/`): CGO 包装器，支持性能测试和 ONNX 推理功能
- **Python 脚本** (`python/`): 基础分词工具

### 关键架构模式
- Rust 代码编译为 C 动态库 (`cdylib`)
- Go 绑定使用 CGO 与 Rust 库交互
- 两种编码方式：完整的 `Encoding`（来自 tokenizers 库）和简化的 `SimpleEncoding`（自定义结构，仅包含 ids, type_ids, attention_mask）
- 内存管理遵循 C 模式，FFI 层中需要显式调用 `free_string`

## 常用开发命令

### Rust
```bash
# 构建 Rust 库
cargo build --release

# 运行 Rust 测试
cargo test

# 构建并运行主示例
cargo run
```

### Go
```bash
# 进入 go_binding/ 目录
cd go_binding

# 运行测试
go test ./test/...

# 运行特定的分词器测试
go test -v ./test/tokenizer_test.go

# 构建 Go 模块
go build ./...
```

## 关键文件和位置

### Rust 核心
- `src/lib.rs` - 分词器操作的 C FFI 函数
- `src/tokenizer_wrapper.rs` - 带有 SimpleEncoding 结构的 Rust API
- `src/main.rs` - 中文文本使用示例

### Go 集成
- `go_binding/tokenizer_wrapper/tokenizer_wrapper.go` - 主要的 Go 包装器
- `go_binding/test/tokenizer_test.go` - 性能基准测试
- `go_binding/inference/` - ONNX 推理功能，支持嵌入模型

### 配置
- 分词器模型存储在 `python/tokenizer/ernie-3.0-nano-zh/`
- 动态库位于 `go_binding/tokenizer_wrapper/lib/` 和 `go_binding/tokenizer_wrapper_v2/lib/`

## 内存管理注意事项
- Rust FFI 函数返回必须用 `free_string` 释放的原始指针
- Go 包装器在 defer 块中处理 C 字符串的分配/释放
- TokenizerWrapper 结构体维护指向 Rust 分词器实例的 unsafe.Pointer

## 测试策略
- Go 测试专注于可配置批次大小的并发性能
- Rust main.rs 作为集成测试，使用中文诗歌样本
- 模型可以本地加载或从 Hugging Face 加载（由 `use_local` 参数控制）